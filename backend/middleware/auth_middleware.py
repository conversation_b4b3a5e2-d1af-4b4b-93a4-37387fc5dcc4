"""
Authentication middleware.

This module provides authentication middleware for the backend.
"""

import os
import logging
from typing import Optional, Dict, Any, List

try:
    from fastapi import Request, HTTPException, status
    import jwt
    from jwt.exceptions import PyJWTError
    from pydantic import BaseModel
except ImportError as e:
    # <PERSON><PERSON> missing dependencies gracefully
    logging.warning(f"Missing dependency: {e}")
    # Define minimal stubs for type checking
    class Request:
        pass
    class HTTPException(Exception):
        pass
    class status:
        HTTP_401_UNAUTHORIZED = 401
    class PyJWTError(Exception):
        pass
    class BaseModel:
        pass

# Configure logging
logger = logging.getLogger(__name__)


class UserContext(BaseModel):
    """User context for authentication."""

    user_id: str
    email: Optional[str] = None
    role: str = "user"
    firm_id: str
    permissions: List[str] = []
    is_authenticated: bool = True

    class Config:
        # Allow arbitrary types for compatibility
        arbitrary_types_allowed = True


def validate_jwt(token: str) -> Dict[str, Any]:
    """
    Verify JWT token and extract claims.

    Args:
        token: The JWT token

    Returns:
        Dict[str, Any]: The JWT claims

    Raises:
        PyJWTError: If token verification fails
    """
    jwt_secret = os.environ.get("SUPABASE_JWT_SECRET", "")

    if not jwt_secret:
        logger.warning("No JWT secret configured, using insecure default")
        # For development only, use a default secret
        if os.getenv("APP_ENV", "development") == "development":
            jwt_secret = "0123456789abcdef0123456789abcdef"
        else:
            raise PyJWTError("JWT secret not configured")

    # Decode and verify the token
    payload = jwt.decode(
        token,
        jwt_secret,
        algorithms=["HS256"],
        options={"verify_signature": True},
    )

    return payload


async def get_current_user(request: Request) -> UserContext:
    """
    Get the current authenticated user from request state.

    This function is designed to be used as a dependency in route handlers.
    It expects the JWT middleware to have already processed the request
    and populated request.state.user.

    Args:
        request: The FastAPI request object

    Returns:
        UserContext: The authenticated user context

    Raises:
        HTTPException: If the user is not authenticated
    """
    if not hasattr(request.state, "user"):
        # Try to extract and validate JWT token if not already done
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication required",
            )

        token = auth_header.split(" ")[1]
        try:
            claims = validate_jwt(token)

            # Extract firm_id from tenant_id in the token
            firm_id = claims.get("tenant_id")
            if not firm_id:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid authentication token: missing tenant_id",
                )

            # Create user context
            user_context = UserContext(
                user_id=claims.get("sub"),
                email=claims.get("email"),
                role=claims.get("role", "user"),
                firm_id=firm_id,
                permissions=claims.get("permissions", []),
                is_authenticated=True,
            )

            # Store in request state for future use
            request.state.user = user_context
            request.state.user_id = claims.get("sub")
            request.state.firm_id = firm_id
            request.state.role = claims.get("role", "user")
            request.state.email = claims.get("email")

            return user_context

        except PyJWTError as e:
            logger.warning(f"JWT verification failed: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication token",
            )

    return request.state.user
