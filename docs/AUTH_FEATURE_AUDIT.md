# Authentication & Feature-Flag Infrastructure Audit

## Executive Summary

This audit report documents the existing authentication and feature-flag infrastructure in the Core AiLex Turborepo to enable reuse for the AI Voice Receptionist (AVR) and avoid duplication.

**Status**: ✅ **Ready for shared auth** - Core infrastructure is well-established with comprehensive JWT handling, tenant isolation, and feature-flag systems.

## 1. Auth Pipeline Inventory

### 1.1 Supabase Auth Client Setup

| Component         | Path                                    | Description                      |
| ----------------- | --------------------------------------- | -------------------------------- |
| Frontend Client   | `frontend/src/lib/supabase/client.ts`   | Main Supabase client for browser |
| Middleware Client | `frontend/src/middleware.ts`            | SSR-compatible auth client       |
| Backend Auth      | `backend/middleware/auth_middleware.py` | Python JWT verification          |
| Service Mesh      | `docs/service-mesh-authentication.md`   | Service-to-service auth pattern  |

### 1.2 JWT Issuance & Refresh Logic

| Component      | Path                                                          | Description                  |
| -------------- | ------------------------------------------------------------- | ---------------------------- |
| JWT Helper     | `frontend/src/lib/auth/jwt-helper.ts`                         | Token verification utilities |
| JWT Utils      | `frontend/src/lib/auth/jwt-utils.ts`                          | Token generation/validation  |
| Auth Hook      | `supabase/migrations/20250429165800_create_auth_jwt_hook.sql` | Custom JWT claims            |
| JWT Middleware | `src/pi_lawyer/middleware/jwt_middleware.py`                  | Python JWT verification      |

### 1.3 JWT Claims Structure

**Standard Claims**:

- `sub`: User ID (UUID)
- `email`: User email
- `exp`: Expiration timestamp
- `iat`: Issued at timestamp

**Custom Claims** (via `auth.jwt()` hook):

- `tenant_id`: Tenant/firm UUID for multi-tenancy
- `role`: Primary user role (`partner`, `attorney`, `paralegal`, `staff`)
- `firm_role`: Firm-specific role
- `permissions`: Array of permission strings

### 1.4 Authentication Dependencies

| Function                | Path                                       | Description             |
| ----------------------- | ------------------------------------------ | ----------------------- |
| `get_current_user()`    | `backend/middleware/auth_middleware.py:36` | Mock user context (dev) |
| `requireAuth()`         | `docs/AUTHENTICATION_TESTING.md:11`        | API route protection    |
| `withAuth()`            | `docs/AUTHENTICATION_TESTING.md:11`        | HOC for auth            |
| `createServiceClient()` | `docs/AUTHENTICATION_TESTING.md:11`        | Service token client    |

## 2. Subscription / Feature-Flag Hooks

### 2.1 FastAPI Dependencies

| Dependency              | Path                                                     | Description              |
| ----------------------- | -------------------------------------------------------- | ------------------------ |
| `validate_api_key`      | `docs/authentication-middleware.md:78`                   | Service token validation |
| `get_current_user`      | `docs/service-mesh-authentication.md:151`                | JWT user extraction      |
| Subscription middleware | `frontend/src/lib/middleware/subscription-middleware.ts` | Feature access control   |

### 2.2 Feature Access Endpoints

| Endpoint                      | Method | Description                |
| ----------------------------- | ------ | -------------------------- |
| `/tenant/{id}/features`       | GET    | Get tenant feature list    |
| `check_tenant_feature_access` | RPC    | Feature access validation  |
| `/subscription/features`      | GET    | Available features by plan |

### 2.3 React Hooks & Decorators

| Hook/Decorator           | Path                                                        | Description                 |
| ------------------------ | ----------------------------------------------------------- | --------------------------- |
| `useSecureAuth`          | `frontend/src/hooks/useSecureAuth.ts`                       | Enhanced auth with security |
| `useRequireAuth`         | `frontend/src/lib/auth/useRequireAuth.ts`                   | Auth redirect hook          |
| `subscriptionMiddleware` | `frontend/src/lib/middleware/subscription-middleware.ts:30` | Feature gate middleware     |
| `isAGUIEnabled()`        | `frontend/src/lib/features/ag-ui.ts:9`                      | AG-UI feature flag          |

## 3. Database Objects

### 3.1 Auth-Related Tables

| Table                          | Schema     | Description             |
| ------------------------------ | ---------- | ----------------------- |
| `auth.users`                   | `auth`     | Supabase auth users     |
| `tenants.users`                | `tenants`  | Extended user profiles  |
| `tenants.firms`                | `tenants`  | Tenant/firm information |
| `security.device_fingerprints` | `security` | Device tracking         |

### 3.2 Subscription Tables

| Table                         | Schema    | Description          |
| ----------------------------- | --------- | -------------------- |
| `tenants.subscriptions`       | `tenants` | Subscription records |
| `tenants.tenant_entitlements` | `tenants` | Feature entitlements |
| `tenants.subscription_addons` | `tenants` | Add-on features      |
| `tenants.tenant_quotas`       | `tenants` | Usage quotas         |

### 3.3 RPC Functions

| Function                                 | Description              | Usage                  |
| ---------------------------------------- | ------------------------ | ---------------------- |
| `check_tenant_feature_access`            | Validates feature access | Middleware, API routes |
| `update_tenant_quotas_from_subscription` | Auto-updates quotas      | Subscription changes   |
| `create_trial_subscription_for_new_firm` | Creates trial subs       | New tenant onboarding  |
| `auth.jwt()`                             | Custom JWT claims        | Token enhancement      |

## 4. Cross-Service Consumption

### 4.1 Services Using Core JWT

| Service             | Path                                        | Auth Method          | Status    |
| ------------------- | ------------------------------------------- | -------------------- | --------- |
| Calendar CRUD Agent | `backend/agents/interactive/calendar_crud/` | JWT + Service tokens | ✅ Active |
| Task CRUD Agent     | `backend/agents/interactive/task_crud/`     | JWT verification     | ✅ Active |
| Document Processing | `backend/services/`                         | Service mesh pattern | ✅ Active |
| ML Training Service | `ml-training/training/train_model.py:40`    | Service role key     | ✅ Active |

### 4.2 Service-to-Service Patterns

| Pattern                  | Implementation                                                              | Description               |
| ------------------------ | --------------------------------------------------------------------------- | ------------------------- |
| Service Mesh             | `docs/service-mesh-authentication.md`                                       | Next.js ↔ Python backend |
| OAuth Token Manager      | `backend/ailex_auth/token_manager.py`                                       | External API tokens       |
| Auth Service Integration | `backend/agents/interactive/calendar_crud/docs/auth_service_integration.md` | Calendar providers        |

### 4.3 Mock Auth Services

| Service                 | Path                                       | Status                 |
| ----------------------- | ------------------------------------------ | ---------------------- |
| Backend Auth Middleware | `backend/middleware/auth_middleware.py:36` | 🟡 Mock implementation |
| Supabase Client Stub    | `src/pi_lawyer/db/supabase_client_stub.py` | 🟡 Development stub    |

## 5. Gaps & Recommendations

### 5.1 Ready for AVR Integration

✅ **JWT Infrastructure**: Complete with custom claims and tenant isolation  
✅ **Feature Flags**: Robust subscription-based feature gating  
✅ **Service Mesh**: Established pattern for service-to-service auth  
✅ **Database Schema**: Comprehensive tenant and subscription tables  
✅ **Middleware**: Reusable auth middleware for FastAPI services

### 5.2 Missing Components

🔴 **Production Auth Middleware**: Replace mock `get_current_user()` in `backend/middleware/auth_middleware.py`  
🔴 **Supabase Client**: Replace stub implementation in `src/pi_lawyer/db/supabase_client_stub.py`  
🟡 **AVR-Specific Features**: Add voice-related feature flags to subscription system  
🟡 **Rate Limiting**: Implement per-tenant rate limits for voice services

### 5.3 Conflicting Code to Remove

| File                                       | Issue               | Recommendation                     |
| ------------------------------------------ | ------------------- | ---------------------------------- |
| `backend/middleware/auth_middleware.py:44` | Mock user context   | Replace with real JWT verification |
| `src/pi_lawyer/db/supabase_client_stub.py` | Stub implementation | Use real Supabase client           |
| `pythagora-core/core/templates/`           | Template auth code  | Remove if unused                   |

### 5.4 AVR Integration Checklist

- [ ] Add AVR feature flags to `tenants.tenant_entitlements`
- [ ] Create AVR-specific subscription plans
- [ ] Implement voice usage quotas in `tenants.tenant_quotas`
- [ ] Add AVR endpoints to existing auth middleware
- [ ] Configure service tokens for AVR ↔ Core communication
- [ ] Test tenant isolation for voice data

## Conclusion

The Core AiLex infrastructure provides a **solid foundation** for AVR authentication and feature management. The existing JWT system, tenant isolation, and subscription management can be directly reused. Primary work needed is replacing development stubs with production implementations and adding AVR-specific feature flags.

**Estimated Integration Effort**: 2-3 days for full AVR auth integration.
