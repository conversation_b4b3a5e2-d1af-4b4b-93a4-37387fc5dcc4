"""Integration tests for JWT middleware with the API."""
import datetime
import os

from fastapi.testclient import Test<PERSON><PERSON>
from jose import jwt

from src.pi_lawyer.api.runtime import app

client = TestClient(app)

# Helper to create test tokens
def create_test_token(
    user_id="test-user",
    email="<EMAIL>",
    role="user",
    tenant_id="test-tenant",
    exp_minutes=30
):
    secret = os.environ.get("SUPABASE_JWT_SECRET", "test-secret")
    payload = {
        "sub": user_id,
        "email": email,
        "role": role,
        "tenant_id": tenant_id,
        "exp": datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(minutes=exp_minutes),
        "iat": datetime.datetime.now(datetime.timezone.utc)
    }
    return jwt.encode(payload, secret, algorithm="HS256")

# Tests
def test_health_endpoint_without_token():
    """Test that the health endpoint is accessible without a token."""
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json()["status"] == "ok"

def test_protected_api_with_valid_token(monkeypatch):
    """Test that a protected API endpoint is accessible with a valid token."""
    # Mock the JWT secret
    monkeypatch.setenv("SUPABASE_JWT_SECRET", "test-secret")
    
    token = create_test_token()
    response = client.get(
        "/api/documents",
        headers={"Authorization": f"Bearer {token}"}
    )
    # Even if the endpoint doesn't exist, we should get a 404, not a 401
    assert response.status_code != 401

def test_protected_api_with_invalid_token(monkeypatch):
    """Test that a protected API endpoint is not accessible with an invalid token."""
    # Mock the JWT secret
    monkeypatch.setenv("SUPABASE_JWT_SECRET", "test-secret")
    
    # Create expired token
    token = create_test_token(exp_minutes=-30)
    response = client.get(
        "/api/documents",
        headers={"Authorization": f"Bearer {token}"}
    )
    assert response.status_code == 401
    assert response.json()["detail"] == "Invalid authentication token"

def test_copilotkit_endpoint_with_valid_key(monkeypatch):
    """Test that the CopilotKit endpoint is accessible with a valid endpoint key."""
    # Mock the endpoint secret
    endpoint_secret = "test-endpoint-secret"
    monkeypatch.setenv("CPK_ENDPOINT_SECRET", endpoint_secret)
    
    response = client.post(
        "/copilotkit/invoke",
        headers={"X-CPK-Endpoint-Key": endpoint_secret},
        json={"action": "test"}
    )
    # We might get an error from CopilotKit, but not a 403
    assert response.status_code != 403

def test_copilotkit_endpoint_with_invalid_key(monkeypatch):
    """Test that the CopilotKit endpoint is not accessible with an invalid endpoint key."""
    # Mock the endpoint secret
    endpoint_secret = "test-endpoint-secret"
    monkeypatch.setenv("CPK_ENDPOINT_SECRET", endpoint_secret)
    
    response = client.post(
        "/copilotkit/invoke",
        headers={"X-CPK-Endpoint-Key": "wrong-secret"},
        json={"action": "test"}
    )
    assert response.status_code == 403
    assert response.json()["detail"] == "Invalid endpoint key"
