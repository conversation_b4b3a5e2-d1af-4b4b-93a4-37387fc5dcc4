# Supabase Configuration
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key
SUPABASE_SERVICE_KEY=your_supabase_service_key
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
PINECONE_API_KEY=your_pinecone_api_key
PINECONE_ENVIRONMENT=us-east-1
PINECONE_INDEX_NAME=new-texas-laws
GCS_BUCKET_NAME=texas-laws-personalinjury
GCS_SERVICE_ACCOUNT_FILE=/path/to/service-account.json
OPENAI_API_KEY=your_openai_api_key
SUPABASE_ACCESS_TOKEN=your_supabase_access_token
SUPABASE_JWT_SECRET=your_jwt_secret
DB_PASSWORD=your_db_password
DB_USER=postgres
DB_NAME=postgres
DB_HOST=your_db_host
DB_PORT=5432
NEXT_PUBLIC_TURNSTILE_SITE_KEY=your_turnstile_site_key
TURNSTILE_SECRET_KEY=your_turnstile_secret_key

# CopilotKit Configuration
NEXT_PUBLIC_COPILOT_API_KEY=your_copilot_api_key

# LLM Selection
DEFAULT_LLM_MODEL=openai/gpt-3.5-turbo
DEFAULT_LLM_TEMPERATURE=0.2
